from sentence_transformers import SentenceTransformer
import faiss
import numpy as np

class VectorStore:
    def __init__(self):
        self.texts = []
        self.index = faiss.IndexFlatL2(384)  # 384 for 'all-MiniLM-L6-v2'
        self.model = SentenceTransformer('all-MiniLM-L6-v2')

    def add_texts(self, text_chunks):
        self.texts.extend(text_chunks)
        vectors = self.model.encode(text_chunks)
        self.index.add(np.array(vectors).astype('float32'))

    def search(self, query, k=3):
        q_vec = self.model.encode([query])
        distances, indices = self.index.search(np.array(q_vec).astype('float32'), k)
        return [self.texts[i] for i in indices[0]]
