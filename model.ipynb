{"cells": [{"cell_type": "code", "execution_count": 3, "id": "e36281db", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_15544\\2207254727.py:11: DeprecationWarning: Substituting font arial by core font helvetica - This is deprecated since v2.7.8, and will soon be removed\n", "  faq_pdf.set_font(\"Arial\", size=12)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_15544\\2207254727.py:28: DeprecationWarning: The parameter \"txt\" has been renamed to \"text\" in 2.7.6\n", "  faq_pdf.cell(200, 10, txt=line, ln=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_15544\\2207254727.py:28: DeprecationWarning: The parameter \"ln\" is deprecated since v2.5.2. Instead of ln=True use new_x=XPos.LMARGIN, new_y=YPos.NEXT.\n", "  faq_pdf.cell(200, 10, txt=line, ln=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["All files created successfully in the docs/ directory!\n"]}], "source": ["from fpdf import FPDF\n", "import pandas as pd\n", "import os\n", "\n", "# Create docs directory if it doesn't exist\n", "os.makedirs(\"docs\", exist_ok=True)\n", "\n", "# --- Create product_faq.pdf ---\n", "faq_pdf = FPDF()\n", "faq_pdf.add_page()\n", "faq_pdf.set_font(\"Arial\", size=12)\n", "faq_text = [\n", "    \"Frequently Asked Questions (FAQ)\",\n", "    \"\",\n", "    \"Q1: What is the warranty period for the product?\",\n", "    \"A1: The product comes with a 1-year standard warranty.\",\n", "    \"\",\n", "    \"Q2: Can the product be used internationally?\",\n", "    \"A2: Yes, it supports universal voltage (100-240V).\",\n", "    \"\",\n", "    \"Q3: What is the return policy?\",\n", "    \"A3: Returns are accepted within 30 days of purchase with a valid receipt.\",\n", "    \"\",\n", "    \"Q4: Is there a mobile app for the product?\",\n", "    \"A4: Yes, it is available on both Android and iOS platforms.\",\n", "]\n", "for line in faq_text:\n", "    faq_pdf.cell(200, 10, txt=line, ln=True)\n", "faq_pdf.output(\"docs/product_faq.pdf\")\n", "\n", "# --- Create product_specs.xlsx ---\n", "specs_data = {\n", "    \"Feature\": [\"Model\", \"Battery Life\", \"Weight\", \"Dimensions\", \"Connectivity\", \"Operating System\"],\n", "    \"Details\": [\"X200 Pro\", \"10 hours\", \"1.2 kg\", \"25 x 18 x 2 cm\", \"Wi-Fi, Bluetooth 5.0\", \"Windows 11 / macOS\"]\n", "}\n", "specs_df = pd.DataFrame(specs_data)\n", "specs_df.to_excel(\"docs/product_specs.xlsx\", index=False)\n", "\n", "# --- Create your_website_text.txt ---\n", "website_text = \"\"\"Welcome to the official product page for the X200 Pro Smart Device.\n", "\n", "The X200 Pro is designed for efficiency and portability, ideal for professionals and students alike.\n", "It features a full HD touchscreen, long battery life, and seamless integration with cloud services.\n", "With its durable build and sleek design, the X200 Pro is your perfect everyday companion.\n", "\n", "For support, drivers, and downloads, visit the Support section of our website.\n", "Stay updated with the latest firmware to ensure optimal performance.\n", "\n", "Contact us anytime via our 24/7 chat support.\n", "\"\"\"\n", "\n", "with open(\"docs/your_website_text.txt\", \"w\") as f:\n", "    f.write(website_text)\n", "\n", "print(\"All files created successfully in the docs/ directory!\")\n"]}], "metadata": {"kernelspec": {"display_name": "aiagentintern", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}